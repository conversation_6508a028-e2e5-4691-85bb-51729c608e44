{"name": "backend", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/server.js", "start": "node src/server.js"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "dependencies": {"@arcjet/inspect": "^1.0.0-beta.8", "@arcjet/node": "^1.0.0-beta.8", "@clerk/express": "^1.7.0", "cloudinary": "^2.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "mongoose": "^8.16.0", "multer": "^2.0.1"}}