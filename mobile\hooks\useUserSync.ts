import { useEffect, useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@clerk/clerk-expo";
import { useApiClient, userApi } from "../utils/api";

export const useUserSync = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const api = useApiClient();
  const hasSyncedRef = useRef(false);

  const syncUserMutation = useMutation({
    mutationFn: () => userApi.syncUser(api),
    onSuccess: (response: any) => {
      console.log("User synced successfully:", response.data.user);
      hasSyncedRef.current = true;
    },
    onError: (error: any) => {
      console.error("User sync failed:", error);
      // Log more detailed error information
      if (error.response) {
        console.error("Error response:", error.response.data);
        console.error("Error status:", error.response.status);
      }
    },
  });

  // auto-sync user when signed in
  useEffect(() => {
    // Only sync if:
    // 1. Auth is loaded (to avoid premature calls)
    // 2. User is signed in
    // 3. Haven't synced yet
    // 4. Not currently syncing
    if (
      isLoaded &&
      isSignedIn &&
      !hasSyncedRef.current &&
      !syncUserMutation.isPending
    ) {
      console.log("Attempting to sync user...");
      syncUserMutation.mutate();
    }
  }, [isLoaded, isSignedIn, syncUserMutation.isPending]);

  return {
    isLoading: syncUserMutation.isPending,
    error: syncUserMutation.error,
    isSuccess: syncUserMutation.isSuccess,
  };
};
