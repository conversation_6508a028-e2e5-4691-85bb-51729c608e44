import { aj } from "../config/arcjet.js";

// Arcjet middleware for rate limiting, bot protection, and security

export const arcjetMiddleware = async (req, res, next) => {
  try {
    // Check if this is a mobile app request
    const userAgent = req.get("User-Agent") || "";
    const isMobileApp =
      userAgent.includes("ReactNative") ||
      userAgent.includes("Expo") ||
      userAgent.includes("X-Clone-ReactNative-Mobile-App");

    // Check if user is authenticated (has Authorization header)
    const hasAuthToken = req.get("Authorization");

    // Skip Arcjet for authenticated mobile app requests during development
    if (isMobileApp && hasAuthToken && process.env.NODE_ENV !== "production") {
      console.log("Bypassing Arcjet for authenticated mobile app request");
      return next();
    }

    const decision = await aj.protect(req, {
      requested: 1, // each request consumes 1 token
    });

    // handle denied requests
    if (decision.isDenied()) {
      if (decision.reason.isRateLimit()) {
        return res.status(429).json({
          error: "Too Many Requests",
          message: "Rate limit exceeded. Please try again later.",
        });
      } else if (decision.reason.isBot()) {
        // Log more details for debugging
        console.log("Bot detection triggered:", {
          userAgent,
          ip: req.ip,
          path: req.path,
          method: req.method,
        });

        return res.status(403).json({
          error: "Bot access denied",
          message: "Automated requests are not allowed.",
        });
      } else {
        return res.status(403).json({
          error: "Forbidden",
          message: "Access denied by security policy.",
        });
      }
    }

    // check for spoofed bots
    if (
      decision.results.some(
        (result) => result.reason.isBot() && result.reason.isSpoofed()
      )
    ) {
      return res.status(403).json({
        error: "Spoofed bot detected",
        message: "Malicious bot activity detected.",
      });
    }

    next();
  } catch (error) {
    console.error("Arcjet middleware error:", error);
    // allow request to continue if Arcjet fails
    next();
  }
};
