// import arcjet, { tokenBucket, shield, detectBot } from "@arcjet/node";
// import { ENV } from "./env.js";

// // initialize Arcjet with security rules
// export const aj = arcjet({
//   key: ENV.ARCJET_KEY,
//   characteristics: ["ip.src"],
//   rules: [
//     // shield protects your app from common attacks e.g. SQL injection, XSS, CSRF attacks
//     shield({ mode: "LIVE" }),

//     // bot detection - block all bots except search engines and mobile apps
//     detectBot({
//       mode: "LIVE",
//       allow: [
//         "CATEGORY:SEARCH_ENGINE",
//         "CATEGORY:MOBILE", // Allow mobile apps
//         // Allow specific user agents for React Native/Expo
//         "USER_AGENT:*Expo*",
//         "USER_AGENT:*ReactNative*",
//         "USER_AGENT:*okhttp*", // Android HTTP client
//         "USER_AGENT:*CFNetwork*", // iOS HTTP client
//         // allow legitimate search engine bots
//         // see full list at https://arcjet.com/bot-list
//       ],
//     }),

//     // rate limiting with token bucket algorithm - more lenient for mobile apps
//     tokenBucket({
//       mode: "LIVE",
//       refillRate: 20, // increased tokens added per interval
//       interval: 10, // interval in seconds (10 seconds)
//       capacity: 30, // increased maximum tokens in bucket
//     }),
//   ],
// });











import arcjet, { tokenBucket, shield, detectBot } from "@arcjet/node";
import { ENV } from "./env.js";

// initialize Arcjet with security rules
export const aj = arcjet({
  key: ENV.ARCJET_KEY,
  characteristics: ["ip.src"],
  rules: [
    // shield protects your app from common attacks e.g. SQL injection, XSS, CSRF attacks
    shield({ mode: "LIVE" }),

    // bot detection - block all bots except search engines and mobile apps
    detectBot({
      mode: "LIVE",
      allow: [
        "CATEGORY:SEARCH_ENGINE",
        "CATEGORY:MOBILE", // Allow mobile apps
        // Allow specific user agents for React Native/Expo
        "USER_AGENT:*Expo*",
        "USER_AGENT:*ReactNative*",
        "USER_AGENT:*okhttp*", // Android HTTP client
        "USER_AGENT:*CFNetwork*", // iOS HTTP client
        // allow legitimate search engine bots
        // see full list at https://arcjet.com/bot-list
      ],
    }),

    // rate limiting with token bucket algorithm - more lenient for mobile apps
    tokenBucket({
      mode: "LIVE",
      refillRate: 20, // increased tokens added per interval
      interval: 10, // interval in seconds (10 seconds)
      capacity: 30, // increased maximum tokens in bucket
    }),
  ],
});
